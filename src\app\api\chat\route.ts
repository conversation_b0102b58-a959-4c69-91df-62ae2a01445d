import { NextResponse } from "next/server";
import { campus } from "@/data/campus";

export const runtime = "nodejs";
export const dynamic = "force-dynamic";

// Simple in-memory, rule-based academic Q&A using campus data
export async function POST(req: Request) {
  const { question } = await req.json();
  const q: string = String(question || "").toLowerCase();
  function listAllPrograms() {
    const programs = campus.colleges.flatMap((college) => 
      college.departments.flatMap((dept) => 
        dept.programs.map((program) => `${program.name} (${college.name})`)
      )
    );
    return `Programs offered: \n- ${programs.join("\n- ")}`;
  }
  
  function listRequirementsFor(collegeName: string) {
    const college = campus.colleges.find((c) => c.name.toLowerCase().includes(collegeName) || c.id.toLowerCase().includes(collegeName));
    if (!college) return "I couldn't find that college.";
    
    const programs = college.departments.flatMap((dept) => dept.programs);
    if (programs.length === 0) return `No programs found for ${college.name}.`;
    
    const programsList = programs.map((program) => 
      `${program.name}:\n  - ${program.requirements.join("\n  - ")}`
    ).join("\n\n");
    
    return `Requirements for ${college.name}:\n\n${programsList}`;
  }
  
  function listDepartments(collegeName: string) {
    const college = campus.colleges.find((c) => c.name.toLowerCase().includes(collegeName) || c.id.toLowerCase().includes(collegeName));
    if (!college) return "I couldn't find that college.";
    return `Departments in ${college.name}:\n- ${college.departments.map(d => d.name).join("\n- ")}`;
  }
  let answer = "";
  if (/programs|courses|offer/.test(q)) {
    answer = listAllPrograms();
  } else if (/requirement|enroll|shift/.test(q)) {
    const target = campus.colleges.find((c) => q.includes(c.name.toLowerCase()) || q.includes(c.id.toLowerCase()));
    answer = target ? listRequirementsFor(target.name.toLowerCase()) : "Please specify the college (e.g., Engineering, Agriculture, Arts and Sciences).";
  } else if (/department|dept/.test(q)) {
    const target = campus.colleges.find((c) => q.includes(c.name.toLowerCase()) || q.includes(c.id.toLowerCase()));
    answer = target ? listDepartments(target.name.toLowerCase()) : "Please specify the college (e.g., Engineering, Agriculture, Arts and Sciences).";
  } else {
    answer = "I can help with programs, departments, and requirements. Try: 'What programs does the College of Agriculture offer?' or 'What are the requirements for Engineering?'";
  }

  return NextResponse.json({ answer });
}
