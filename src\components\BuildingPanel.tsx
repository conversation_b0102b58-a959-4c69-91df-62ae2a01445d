"use client";
import { Building, getDepartmentsByBuilding, getProgramsByBuilding, getCollegeByBuilding } from "@/data/campus";
import { XMarkIcon } from "@heroicons/react/24/solid";

export default function BuildingPanel({ building, onClose }: { building: Building | null; onClose: () => void }) {
  if (!building) {
    return (
      <div className={`fixed inset-y-0 right-0 w-full sm:w-[28rem] z-30 transform transition-transform duration-300 translate-x-full`}>
        <div className="h-full bg-white shadow-xl border-l border-slate-200 flex flex-col">
          <div className="p-4 border-b flex items-center justify-between">
            <h2 className="font-semibold">Details</h2>
            <button className="p-2 rounded hover:bg-slate-100" onClick={onClose} aria-label="Close">
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
          <div className="flex-1 overflow-y-auto p-4">
            <div className="text-sm text-slate-500">Click a building to see details.</div>
          </div>
        </div>
      </div>
    );
  }

  const departments = getDepartmentsByBuilding(building.id);
  const programs = getProgramsByBuilding(building.id);
  const college = getCollegeByBuilding(building.id);

  return (
    <div className={`fixed inset-y-0 right-0 w-full sm:w-[28rem] z-30 transform transition-transform duration-300 translate-x-0`}>
      <div className="h-full bg-white shadow-xl border-l border-slate-200 flex flex-col">
        <div className="p-4 border-b flex items-center justify-between">
          <h2 className="font-semibold">{building.name}</h2>
          <button className="p-2 rounded hover:bg-slate-100" onClick={onClose} aria-label="Close">
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {/* College Information */}
          {college && (
            <section>
              <h3 className="text-sm font-medium text-slate-600">College</h3>
              <div className="mt-2">
                <div className="text-sm font-medium text-slate-900">{college.name}</div>
                <div className="text-xs text-slate-500 mt-1">
                  {building.type === "main" ? "Main Building" : "Additional Building"}
                </div>
              </div>
            </section>
          )}

          {/* Departments */}
          {departments.length > 0 && (
            <section>
              <h3 className="text-sm font-medium text-slate-600">Departments ({departments.length})</h3>
              <div className="mt-2 space-y-2">
                {departments.map((dept) => (
                  <div key={dept.id} className="p-3 rounded-lg border border-slate-200 bg-slate-50">
                    <div className="font-medium text-sm text-slate-900">{dept.name}</div>
                    <div className="text-xs text-slate-500 mt-1">
                      {dept.programs.length} program{dept.programs.length !== 1 ? 's' : ''}
                    </div>
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Programs */}
          {programs.length > 0 && (
            <section>
              <h3 className="text-sm font-medium text-slate-600">Programs ({programs.length})</h3>
              <div className="mt-2 space-y-2">
                {programs.map((program) => (
                  <div key={program.id} className="p-3 rounded-lg border border-slate-200">
                    <div className="font-medium text-sm text-slate-900">{program.name}</div>
                    {program.requirements.length > 0 && (
                      <details className="mt-2">
                        <summary className="text-xs text-slate-600 cursor-pointer hover:text-slate-800">
                          Requirements ({program.requirements.length})
                        </summary>
                        <ul className="mt-2 ml-4 space-y-1">
                          {program.requirements.map((req, index) => (
                            <li key={index} className="text-xs text-slate-600 list-disc">
                              {req}
                            </li>
                          ))}
                        </ul>
                      </details>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Rooms */}
          {building.rooms.length > 0 && (
            <section>
              <h3 className="text-sm font-medium text-slate-600">Rooms ({building.rooms.length})</h3>
              <div className="mt-2 grid grid-cols-2 gap-2">
                {building.rooms.map((room) => (
                  <div key={room.id} className="rounded border border-slate-200 px-2 py-1 text-sm">
                    <div className="font-medium text-slate-900">{room.id}</div>
                    {room.name && (
                      <div className="text-xs text-slate-600">{room.name}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>
          )}

          {/* Building Details */}
          <section>
            <h3 className="text-sm font-medium text-slate-600">Building Details</h3>
            <div className="mt-2 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Building ID:</span>
                <span className="font-mono text-slate-900">{building.id}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">Type:</span>
                <span className="text-slate-900 capitalize">{building.type}</span>
              </div>
              {building.color && (
                <div className="flex justify-between text-sm items-center">
                  <span className="text-slate-600">Color:</span>
                  <div className="flex items-center gap-2">
                    <div 
                      className="w-4 h-4 rounded border border-slate-300"
                      style={{ backgroundColor: building.color }}
                    ></div>
                    <span className="font-mono text-slate-900">{building.color}</span>
                  </div>
                </div>
              )}
            </div>
          </section>

          {/* Empty States */}
          {departments.length === 0 && programs.length === 0 && building.rooms.length === 0 && (
            <div className="text-sm text-slate-500 text-center py-8">
              No departments, programs, or rooms data available for this building.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
