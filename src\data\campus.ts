export type Room = {
  id: string; // unique room identifier based on 3D object name
  name?: string;
  // Offset relative to the building center in world units
  offset: { x: number; y: number; z: number };
};

export type Program = {
  id: string;
  name: string;
  requirements: string[]; // enrollment/shifting requirements
};

export type Department = {
  id: string;
  name: string;
  buildingId: string; // which building this department is located in
  programs: Program[];
};

export type Building = {
  id: string; // matches 3D mesh material name (e.g., "coe-main-building", "cnsm-building-1")
  name: string;
  collegeId: string; // which college this building belongs to
  type: "main" | "additional"; // main building or additional building
  // Center position in world coordinates (leave blank to fill manually)
  position: { x: number; y: number; z: number };
  // Box size in world units (width, height, depth)
  size: { w: number; h: number; d: number };
  color?: string;
  rooms: Room[];
};

export type College = {
  id: string; // short identifier (e.g., "coe", "cnsm", "cssh")
  name: string;
  mainBuildingId: string; // reference to the main building
  buildingIds: string[]; // all buildings belonging to this college (including main)
  departments: Department[];
};

export type CampusData = {
  name: string;
  boundary: { minX: number; maxX: number; minZ: number; maxZ: number };
  colleges: College[];
  buildings: Building[];
};

// NOTE: Sample data based on programs_by_college.json. Positions and sizes to be filled manually.
export const campus: CampusData = {
  name: "MSU - Main Campus",
  boundary: { minX: -50, maxX: 215, minZ: -100, maxZ: 100 },
  colleges: [
    {
      id: "coa",
      name: "College of Agriculture",
      mainBuildingId: "coa-main-building",
      buildingIds: ["coa-main-building"],
      departments: [
        {
          id: "coa-agribusiness",
          name: "Agribusiness Management Department",
          buildingId: "coa-main-building",
          programs: [
            {
              id: "coa-bsam",
              name: "Bachelor of Science in Agribusiness Management",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-bsafp",
              name: "Bachelor of Science in Agriculture Major in Food Processing",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-damfp",
              name: "Diploma in Agribusiness Management Major in Food Processing",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
              ],
            },
          ],
        },
        {
          id: "coa-agext",
          name: "Agricultural Education & Extension Department",
          buildingId: "coa-main-building",
          programs: [
            {
              id: "coa-bsaae",
              name: "Bachelor of Science in Agriculture, Major in Agricultural Extension",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
          ],
        },
        {
          id: "coa-animalscience",
          name: "Animal Science Department",
          buildingId: "coa-main-building",
          programs: [
            {
              id: "coa-bsaas",
              name: "Bachelor of Science in Agriculture major in Animal Science",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-msas",
              name: "Master of Science in Animal Science",
              requirements: [
                "Bachelor's degree in related field",
                "Good Academic Standing",
                "Research Proposal",
                "Thesis Adviser Endorsement",
              ],
            },
          ],
        },
        {
          id: "coa-plantscience",
          name: "Department of Plant Science",
          buildingId: "coa-main-building",
          programs: [
            {
              id: "coa-bsa-agronomy",
              name: "BSA - Agronomy",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-bsa-horticulture",
              name: "BSA - Horticulture",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-bsa-farming",
              name: "BSA - Farming Systems",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "coa-bsa-soil",
              name: "BSA - Soil Science",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "cbaa",
      name: "College of Business Administration and Accountancy",
      mainBuildingId: "cbaa-main-building",
      buildingIds: ["cbaa-main-building"],
      departments: [
        {
          id: "cbaa-accountancy",
          name: "Accountancy Department",
          buildingId: "cbaa-main-building",
          programs: [
            {
              id: "cbaa-bsa",
              name: "Bachelor of Science in Accountancy",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
                "Mathematics Proficiency Test",
              ],
            },
          ],
        },
        {
          id: "cbaa-economics",
          name: "Economics Department",
          buildingId: "cbaa-main-building",
          programs: [
            {
              id: "cbaa-bsbae",
              name: "Bachelor of Science in Business Administration, major in Economics",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
          ],
        },
        {
          id: "cbaa-mba",
          name: "Masters of Business Administration",
          buildingId: "cbaa-main-building",
          programs: [
            {
              id: "cbaa-mba-ibf",
              name: "Master of Business Administration - Islamic Banking and Finance",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
            {
              id: "cbaa-mba-fm",
              name: "Master of Business Administration - Financial Management",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
            {
              id: "cbaa-mba-hrm",
              name: "Master of Business Administration - Human Resource Management",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
            {
              id: "cbaa-mba-mm",
              name: "Master of Business Administration - Marketing Management",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
            {
              id: "cbaa-mba-olm",
              name: "Master of Business Administration - Organizational Leadership and Management",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
            {
              id: "cbaa-mba-sed",
              name: "Master of Business Administration - Sustainable Enterprise Development",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "GMAT/GRE or equivalent",
                "Work Experience (preferred)",
              ],
            },
          ],
        },
        {
          id: "cbaa-management",
          name: "Management Department",
          buildingId: "cbaa-main-building",
          programs: [
            {
              id: "cbaa-bsbam",
              name: "Bachelor of Science in Business Administration, major in Management",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "cbaa-bsbahrm",
              name: "Bachelor of Science in Business Administration, major in Human Resource Management",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
          ],
        },
        {
          id: "cbaa-marketing",
          name: "Marketing Department",
          buildingId: "cbaa-main-building",
          programs: [
            {
              id: "cbaa-bsbamm-adv",
              name: "Bachelor of Science in Business Administration, major in Marketing Management - Advertising",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "cbaa-bsbamm-dm",
              name: "Bachelor of Science in Business Administration, major in Marketing Management - Digital Marketing",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
            {
              id: "cbaa-bsbae",
              name: "Bachelor of Science in Business Administration, major in Entrepreneurship",
              requirements: [
                "High School Diploma or equivalent",
                "Good Moral Certificate",
                "Medical Certificate",
                "Entrance Examination",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "coe",
      name: "College of Engineering",
      mainBuildingId: "coe-main-building",
      buildingIds: ["coe-main-building"],
      departments: [
        {
          id: "coe-abe",
          name: "Agricultural and Biosystems Engineering Department",
          buildingId: "coe-main-building",
          programs: [
            {
              id: "coe-bsabe",
              name: "Bachelor of Science in Agricultural and Biosystems Engineering",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Chemistry prerequisites",
              ],
            },
          ],
        },
        {
          id: "coe-che",
          name: "Chemical Engineering Department",
          buildingId: "coe-main-building",
          programs: [
            {
              id: "coe-bsche",
              name: "Bachelor of Science in Chemical Engineering",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Chemistry prerequisites",
              ],
            },
          ],
        },
        {
          id: "coe-ce",
          name: "Civil Engineering Department",
          buildingId: "coe-main-building",
          programs: [
            {
              id: "coe-bsce",
              name: "Bachelor of Science in Civil Engineering",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Mathematics prerequisites",
              ],
            },
          ],
        },
        {
          id: "coe-eee",
          name: "Electrical and Electronics Engineering Department",
          buildingId: "coe-main-building",
          programs: [
            {
              id: "coe-bsee",
              name: "Bachelor of Science in Electrical Engineering",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Mathematics prerequisites",
              ],
            },
            {
              id: "coe-bsece",
              name: "Bachelor of Science in Electronics Engineering",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Mathematics prerequisites",
              ],
            },
          ],
        },
        {
          id: "coe-me",
          name: "Mechanical Engineering Department",
          buildingId: "coe-main-building",
          programs: [
            {
              id: "coe-bsme",
              name: "Bachelor of Science in Mechanical Engineering",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Engineering Aptitude Test",
                "Physics and Mathematics prerequisites",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "cnsm",
      name: "College of Natural Sciences and Mathematics",
      mainBuildingId: "cnsm-main-building",
      buildingIds: ["cnsm-main-building"],
      departments: [
        {
          id: "cnsm-biology",
          name: "Biology Department",
          buildingId: "cnsm-main-building",
          programs: [
            {
              id: "cnsm-bsbio",
              name: "Bachelor of Science in Biology (Animal Biology)",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Science Aptitude Test",
                "Biology and Chemistry prerequisites",
              ],
            },
            {
              id: "cnsm-msbio",
              name: "Master of Science in Biology",
              requirements: [
                "Bachelor's degree in Biology or related field",
                "Good Academic Standing",
                "Research Proposal",
                "Thesis Adviser Endorsement",
              ],
            },
            {
              id: "cnsm-phdbio",
              name: "Doctor of Philosophy in Biology",
              requirements: [
                "Master's degree in Biology or related field",
                "Good Academic Standing",
                "Comprehensive Examination",
                "Dissertation Proposal",
                "Research Publications",
              ],
            },
          ],
        },
        {
          id: "cnsm-chemistry",
          name: "Chemistry Department",
          buildingId: "cnsm-main-building",
          programs: [
            {
              id: "cnsm-bschem",
              name: "Bachelor of Science in Chemistry",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Science Aptitude Test",
                "Chemistry and Mathematics prerequisites",
              ],
            },
          ],
        },
        {
          id: "cnsm-mathematics",
          name: "Mathematics Department",
          buildingId: "cnsm-main-building",
          programs: [
            {
              id: "cnsm-bsmath",
              name: "Bachelor of Science in Mathematics",
              requirements: [
                "High School Diploma with strong Mathematics background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Mathematics Aptitude Test",
              ],
            },
            {
              id: "cnsm-bsstat",
              name: "Bachelor of Science in Statistics",
              requirements: [
                "High School Diploma with strong Mathematics background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Mathematics Aptitude Test",
              ],
            },
            {
              id: "cnsm-msmath",
              name: "Master of Science in Mathematics",
              requirements: [
                "Bachelor's degree in Mathematics or related field",
                "Good Academic Standing",
                "Research Proposal",
                "Thesis Adviser Endorsement",
              ],
            },
            {
              id: "cnsm-msthsm",
              name: "Master of Science in Teaching High School Mathematics",
              requirements: [
                "Bachelor's degree in Mathematics or Education",
                "Teaching Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "cnsm-phdmath",
              name: "Doctor of Philosophy in Mathematics",
              requirements: [
                "Master's degree in Mathematics or related field",
                "Good Academic Standing",
                "Comprehensive Examination",
                "Dissertation Proposal",
              ],
            },
            {
              id: "cnsm-certstat",
              name: "Certificate in Statistics",
              requirements: [
                "Bachelor's degree in any field",
                "Basic Mathematics background",
              ],
            },
          ],
        },
        {
          id: "cnsm-physics",
          name: "Physics Department",
          buildingId: "cnsm-main-building",
          programs: [
            {
              id: "cnsm-bsphys",
              name: "Bachelor of Science in Physics",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Physics Aptitude Test",
                "Mathematics and Physics prerequisites",
              ],
            },
            {
              id: "cnsm-msphys",
              name: "Master of Science in Physics",
              requirements: [
                "Bachelor's degree in Physics or related field",
                "Good Academic Standing",
                "Research Proposal",
                "Thesis Adviser Endorsement",
              ],
            },
            {
              id: "cnsm-phdphys",
              name: "Doctor of Philosophy in Physics",
              requirements: [
                "Master's degree in Physics or related field",
                "Good Academic Standing",
                "Comprehensive Examination",
                "Dissertation Proposal",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "cssh",
      name: "College of Social Sciences and Humanities",
      mainBuildingId: "cssh-main-building",
      buildingIds: ["cssh-main-building"],
      departments: [
        {
          id: "cssh-cms",
          name: "Department of Communication and Media Studies",
          buildingId: "cssh-main-building",
          programs: [
            {
              id: "cssh-bajour",
              name: "Bachelor of Arts in Journalism",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
                "Writing Portfolio",
              ],
            },
            {
              id: "cssh-bacms",
              name: "Bachelor of Arts in Communication and Media Studies (Media Education)",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
              ],
            },
            {
              id: "cssh-bsdevcom",
              name: "Bachelor of Science in Development Communication",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
              ],
            },
          ],
        },
        {
          id: "cssh-english",
          name: "English Department",
          buildingId: "cssh-main-building",
          programs: [
            {
              id: "cssh-baels",
              name: "Bachelor of Arts in English Language Studies",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
              ],
            },
            {
              id: "cssh-balcs",
              name: "Bachelor of Arts in Literary and Cultural Studies",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
              ],
            },
            {
              id: "cssh-maelt",
              name: "Master of Arts in English Language Teaching",
              requirements: [
                "Bachelor's degree in English or related field",
                "Teaching Experience",
                "Good Academic Standing",
                "English Proficiency Certificate",
              ],
            },
          ],
        },
        {
          id: "cssh-filipino",
          name: "Filipino Department",
          buildingId: "cssh-main-building",
          programs: [
            {
              id: "cssh-bafil",
              name: "Batsilyer sa Arte sa Filipino",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Filipino Proficiency Test",
              ],
            },
            {
              id: "cssh-bapan",
              name: "Batsilyer sa Arte sa Panitikan",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Filipino Proficiency Test",
              ],
            },
            {
              id: "cssh-mafil-ling",
              name: "Master ng Sining sa Filipino medyor sa Linguwistika",
              requirements: [
                "Bachelor's degree in Filipino or related field",
                "Good Academic Standing",
                "Filipino Proficiency Certificate",
              ],
            },
            {
              id: "cssh-mafil-lit",
              name: "Master ng Sining sa Filipino medyor sa Literatura",
              requirements: [
                "Bachelor's degree in Filipino or related field",
                "Good Academic Standing",
                "Filipino Proficiency Certificate",
              ],
            },
          ],
        },
        {
          id: "cssh-history",
          name: "History Department",
          buildingId: "cssh-main-building",
          programs: [
            {
              id: "cssh-bahist",
              name: "Bachelor of Arts in History",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Social Studies Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "cssh-lis",
          name: "Department of Library and Information Science",
          buildingId: "cssh-main-building",
          programs: [
            {
              id: "cssh-blis",
              name: "Bachelor of Library and Information Science",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Information Literacy Test",
              ],
            },
          ],
        },
        {
          id: "cssh-philosophy",
          name: "Department of Philosophy",
          buildingId: "cssh-main-building",
          programs: [
            {
              id: "cssh-baphil",
              name: "Bachelor of Arts in Philosophy",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Critical Thinking Assessment",
              ],
            },
          ],
        },
        {
          id: "cssh-polsci",
          name: "Department of Political Studies",
          buildingId: "cssh-main-building",
          programs: [
            {
              id: "cssh-baps",
              name: "Bachelor of Arts in Political Science",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Social Studies Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "cssh-psychology",
          name: "Department of Psychology",
          buildingId: "cssh-main-building",
          programs: [
            {
              id: "cssh-bspsych",
              name: "Bachelor of Science in Psychology",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Psychological Assessment",
                "Interview",
              ],
            },
          ],
        },
        {
          id: "cssh-sociology",
          name: "Department of Sociology",
          buildingId: "cssh-main-building",
          programs: [
            {
              id: "cssh-basoc",
              name: "Bachelor of Arts in Sociology",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Social Studies Aptitude Test",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "ced",
      name: "College of Education",
      mainBuildingId: "ced-main-building",
      buildingIds: ["ced-main-building"],
      departments: [
        {
          id: "ced-elementary",
          name: "Elementary Teaching Department",
          buildingId: "ced-main-building",
          programs: [
            {
              id: "ced-beed",
              name: "Bachelor of Elementary Education",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Interview",
              ],
            },
            {
              id: "ced-beced",
              name: "Bachelor of Early Childhood Education",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Interview",
              ],
            },
          ],
        },
        {
          id: "ced-secondary",
          name: "Secondary Teaching Department",
          buildingId: "ced-main-building",
          programs: [
            {
              id: "ced-bsed-sci",
              name: "BSEd Sciences",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Interview",
              ],
            },
            {
              id: "ced-bsed-math",
              name: "BSEd Mathematics",
              requirements: [
                "High School Diploma with strong Math background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Interview",
              ],
            },
            {
              id: "ced-bsed-eng",
              name: "BSEd English",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "English Proficiency Test",
                "Interview",
              ],
            },
            {
              id: "ced-bsed-fil",
              name: "BSEd Filipino",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Filipino Proficiency Test",
                "Interview",
              ],
            },
            {
              id: "ced-bsed-ss",
              name: "BSEd Social Studies",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Teaching Aptitude Test",
                "Interview",
              ],
            },
          ],
        },
        {
          id: "ced-homeec",
          name: "Department of Home Economics",
          buildingId: "ced-main-building",
          programs: [
            {
              id: "ced-btle",
              name: "Bachelor of Technology and Livelihood Education",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Skills Assessment",
              ],
            },
            {
              id: "ced-btvte",
              name: "Bachelor of Technical-Vocational Teacher Education",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Skills Assessment",
                "Teaching Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "ced-graduate",
          name: "Graduate Education Department",
          buildingId: "ced-main-building",
          programs: [
            {
              id: "ced-phdem",
              name: "Doctor of Philosophy in Educational Management",
              requirements: [
                "Master's degree in Education or related field",
                "Administrative Experience",
                "Comprehensive Examination",
                "Dissertation Proposal",
              ],
            },
            {
              id: "ced-maed-reading",
              name: "Master of Arts in Education major in Reading Program",
              requirements: [
                "Bachelor's degree in Education or related field",
                "Teaching Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "ced-maed-guidance",
              name: "Master of Arts in Education Major in Guidance and Counseling",
              requirements: [
                "Bachelor's degree in Education, Psychology or related field",
                "Counseling Experience (preferred)",
                "Good Academic Standing",
              ],
            },
            {
              id: "ced-maed-em",
              name: "Master of Arts in Education major in Educational Management",
              requirements: [
                "Bachelor's degree in Education or related field",
                "Administrative Experience (preferred)",
                "Good Academic Standing",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "det",
      name: "Division of Engineering Technology",
      mainBuildingId: "det-main-building",
      buildingIds: ["det-main-building"],
      departments: [
        {
          id: "det-graduate",
          name: "Graduate Studies",
          buildingId: "det-main-building",
          programs: [
            {
              id: "det-msiet-ce",
              name: "Master of Science in Industrial Engineering Technology Major in Construction Engineering",
              requirements: [
                "Bachelor's degree in Engineering Technology or related field",
                "Work Experience in Construction",
                "Good Academic Standing",
              ],
            },
            {
              id: "det-msiet-ere",
              name: "Master of Science in Industrial Engineering Technology Major in Electrical and Renewable Energy",
              requirements: [
                "Bachelor's degree in Engineering Technology or related field",
                "Work Experience in Electrical/Energy field",
                "Good Academic Standing",
              ],
            },
            {
              id: "det-msiet-ms",
              name: "Master of Science in Industrial Engineering Technology Major in Materials Science",
              requirements: [
                "Bachelor's degree in Engineering Technology or related field",
                "Work Experience in Materials field",
                "Good Academic Standing",
              ],
            },
          ],
        },
        {
          id: "det-metal",
          name: "Metal Department",
          buildingId: "det-main-building",
          programs: [
            {
              id: "det-det-mst",
              name: "Diploma in Engineering Technology Major in Machine Shop Technology",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
            {
              id: "det-detre",
              name: "Diploma in Electrical Engineering Technology and Renewable Energy",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
            {
              id: "det-bset-maf",
              name: "Bachelor of Science in Engineering Technology Major in Machining and Fabrication",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
            {
              id: "det-bset-ere",
              name: "Bachelor of Science in Engineering Technology Major in Electrical and Renewable Energy",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "det-wood",
          name: "Wood Department",
          buildingId: "det-main-building",
          programs: [
            {
              id: "det-dct",
              name: "Diploma in Engineering Technology Major in Construction Technology",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
            {
              id: "det-bset-cet",
              name: "Bachelor of Science in Engineering Technology Major in Construction Engineering Technology",
              requirements: [
                "High School Diploma with strong Math and Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "cfas",
      name: "College of Fisheries and Aquatic Sciences",
      mainBuildingId: "cfas-main-building",
      buildingIds: ["cfas-main-building"],
      departments: [
        {
          id: "cfas-fisheries",
          name: "Fisheries Department",
          buildingId: "cfas-main-building",
          programs: [
            {
              id: "cfas-bsfish",
              name: "BS Fisheries",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Science Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "cfas-fishtech",
          name: "Fisheries Technology Department",
          buildingId: "cfas-main-building",
          programs: [
            {
              id: "cfas-dtaqua",
              name: "DT in Aquaculture",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
            {
              id: "cfas-dtfp",
              name: "DT in Fish Processing",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Technical Aptitude Test",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "cfes",
      name: "College of Forestry and Environmental Studies",
      mainBuildingId: "cfes-main-building",
      buildingIds: ["cfes-main-building"],
      departments: [
        {
          id: "cfes-graduate",
          name: "Graduate School",
          buildingId: "cfes-main-building",
          programs: [
            {
              id: "cfes-msf",
              name: "Master of Science in Forestry",
              requirements: [
                "Bachelor's degree in Forestry or related field",
                "Work Experience in Forestry",
                "Good Academic Standing",
              ],
            },
            {
              id: "cfes-mesf",
              name: "Master in Ecogovernance and Social Forestry",
              requirements: [
                "Bachelor's degree in Forestry, Environmental Science or related field",
                "Work Experience in Environmental/Forestry field",
                "Good Academic Standing",
              ],
            },
          ],
        },
        {
          id: "cfes-envstudies",
          name: "Department of Environmental Studies",
          buildingId: "cfes-main-building",
          programs: [
            {
              id: "cfes-bsenvs",
              name: "BS Environmental Science",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Environmental Science Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "cfes-forestry",
          name: "Department of Forestry",
          buildingId: "cfes-main-building",
          programs: [
            {
              id: "cfes-bsfor",
              name: "BS Forestry",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Forestry Aptitude Test",
              ],
            },
            {
              id: "cfes-bsfor-agro",
              name: "BS Forestry Major in Agroforestry",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Forestry Aptitude Test",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "chs",
      name: "College of Health Sciences",
      mainBuildingId: "chs-main-building",
      buildingIds: ["chs-main-building"],
      departments: [
        {
          id: "chs-nursing",
          name: "Nursing Department",
          buildingId: "chs-main-building",
          programs: [
            {
              id: "chs-bsn",
              name: "Bachelor of Science in Nursing",
              requirements: [
                "High School Diploma with strong Science background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Nursing Aptitude Test",
                "Interview",
                "Physical and Mental Health Assessment",
              ],
            },
          ],
        },
        {
          id: "chs-graduate",
          name: "Graduate Department",
          buildingId: "chs-main-building",
          programs: [
            {
              id: "chs-msn",
              name: "Master of Science in Nursing",
              requirements: [
                "Bachelor's degree in Nursing",
                "RN License",
                "Clinical Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "chs-man",
              name: "Master of Arts in Nursing",
              requirements: [
                "Bachelor's degree in Nursing",
                "RN License",
                "Clinical Experience",
                "Good Academic Standing",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "chtm",
      name: "College of Hotel and Restaurant Management",
      mainBuildingId: "chtm-main-building",
      buildingIds: ["chtm-main-building"],
      departments: [
        {
          id: "chtm-hospitality",
          name: "Hospitality Management Department",
          buildingId: "chtm-main-building",
          programs: [
            {
              id: "chtm-bshm",
              name: "Bachelor of Science in Hospitality Management",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Interview",
                "Basic English Communication Skills",
              ],
            },
          ],
        },
        {
          id: "chtm-tourism",
          name: "Tourism Management Department",
          buildingId: "chtm-main-building",
          programs: [
            {
              id: "chtm-bstm",
              name: "Bachelor of Science in Tourism Management",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Interview",
                "Basic English Communication Skills",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "cics",
      name: "College of Information and Computing Sciences",
      mainBuildingId: "cics-main-building",
      buildingIds: ["cics-main-building"],
      departments: [
        {
          id: "cics-cs",
          name: "Computer Science Department",
          buildingId: "cics-main-building",
          programs: [
            {
              id: "cics-bscs",
              name: "Bachelor of Science in Computer Science",
              requirements: [
                "High School Diploma with strong Math background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Computer Programming Aptitude Test",
                "Mathematics Proficiency Test",
              ],
            },
          ],
        },
        {
          id: "cics-is",
          name: "Department of Information Sciences",
          buildingId: "cics-main-building",
          programs: [
            {
              id: "cics-bsis",
              name: "Bachelor of Science in Information Systems",
              requirements: [
                "High School Diploma with strong Math background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Information Systems Aptitude Test",
              ],
            },
            {
              id: "cics-bsit-db",
              name: "Bachelor of Science in Information Technology (Database)",
              requirements: [
                "High School Diploma with strong Math background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Information Technology Aptitude Test",
              ],
            },
            {
              id: "cics-bsit-net",
              name: "Bachelor of Science in Information Technology (Network)",
              requirements: [
                "High School Diploma with strong Math background",
                "Good Moral Certificate",
                "Medical Certificate",
                "Information Technology Aptitude Test",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "ise",
      name: "Institute of Science Education",
      mainBuildingId: "ise-main-building",
      buildingIds: ["ise-main-building"],
      departments: [
        {
          id: "ise-graduate",
          name: "Graduate Studies",
          buildingId: "ise-main-building",
          programs: [
            {
              id: "ise-phd-math",
              name: "Ph.D. in Science Education (Mathematics)",
              requirements: [
                "Master's degree in Mathematics Education or related field",
                "Teaching Experience",
                "Research Experience",
                "Comprehensive Examination",
              ],
            },
            {
              id: "ise-phd-bio",
              name: "Ph.D. in Science Education (Biology)",
              requirements: [
                "Master's degree in Biology Education or related field",
                "Teaching Experience",
                "Research Experience",
                "Comprehensive Examination",
              ],
            },
            {
              id: "ise-mst-gs",
              name: "Master of Science in Teaching General Science",
              requirements: [
                "Bachelor's degree in Science or Education",
                "Teaching Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "ise-mst-es",
              name: "Master of Science in Teaching Elementary Science",
              requirements: [
                "Bachelor's degree in Elementary Education or Science",
                "Teaching Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "ise-mst-em",
              name: "Master of Science in Teaching Elementary Mathematics",
              requirements: [
                "Bachelor's degree in Elementary Education or Mathematics",
                "Teaching Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "ise-mse-sm",
              name: "Master of Science Education in Secondary Mathematics",
              requirements: [
                "Bachelor's degree in Mathematics or Education",
                "Teaching Experience",
                "Good Academic Standing",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "kfciaas",
      name: "King Faisal Center for Islamic Arabic and Asian Studies",
      mainBuildingId: "kfciaas-main-building",
      buildingIds: ["kfciaas-main-building"],
      departments: [
        {
          id: "kfciaas-graduate",
          name: "Graduate Program Department",
          buildingId: "kfciaas-main-building",
          programs: [
            {
              id: "kfciaas-mais",
              name: "Master of Arts in Islamic Studies major in Muslim Law",
              requirements: [
                "Bachelor's degree in Islamic Studies or related field",
                "Good Academic Standing",
                "Islamic Studies Background",
                "Arabic Language Proficiency",
              ],
            },
          ],
        },
        {
          id: "kfciaas-ir",
          name: "International Relations Department",
          buildingId: "kfciaas-main-building",
          programs: [
            {
              id: "kfciaas-bsir",
              name: "Bachelor of Science in International Relations",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "English Proficiency Test",
                "Social Studies Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "kfciaas-is",
          name: "Islamic Studies Department",
          buildingId: "kfciaas-main-building",
          programs: [
            {
              id: "kfciaas-bais",
              name: "Bachelor of Arts in Islamic Studies major in Shari'ah",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Islamic Studies Background",
                "Arabic Language Proficiency",
              ],
            },
          ],
        },
        {
          id: "kfciaas-arabic",
          name: "Teaching Arabic Department",
          buildingId: "kfciaas-main-building",
          programs: [
            {
              id: "kfciaas-bsta",
              name: "Bachelor of Science in Teaching Arabic",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Arabic Language Proficiency",
                "Teaching Aptitude Test",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "col",
      name: "College of Law",
      mainBuildingId: "col-main-building",
      buildingIds: ["col-main-building"],
      departments: [
        {
          id: "col-law",
          name: "College of Law",
          buildingId: "col-main-building",
          programs: [
            {
              id: "col-jd",
              name: "Juris Doctor (JD) program",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "Law Aptitude Test",
                "Interview",
                "Character and Fitness Evaluation",
              ],
            },
            {
              id: "col-shariah",
              name: "Shari'ah",
              requirements: [
                "Bachelor's degree in any field",
                "Good Academic Standing",
                "Islamic Law Aptitude Test",
                "Islamic Studies Background",
                "Arabic Language Proficiency",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "cpa",
      name: "College of Public Affairs",
      mainBuildingId: "cpa-main-building",
      buildingIds: ["cpa-main-building"],
      departments: [
        {
          id: "cpa-graduate",
          name: "Graduate Studies",
          buildingId: "cpa-main-building",
          programs: [
            {
              id: "cpa-dpa",
              name: "Doctor of Public Administration (DPA) Program",
              requirements: [
                "Master's degree in Public Administration or related field",
                "Administrative Experience",
                "Research Experience",
                "Comprehensive Examination",
              ],
            },
            {
              id: "cpa-mscd",
              name: "Master of Science in Community Development (MSCD) Program",
              requirements: [
                "Bachelor's degree in Social Sciences or related field",
                "Community Development Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "cpa-mssw",
              name: "Master of Science in Social Work (MSSW) Program",
              requirements: [
                "Bachelor's degree in Social Work or related field",
                "Social Work Experience",
                "Good Academic Standing",
              ],
            },
          ],
        },
        {
          id: "cpa-pa",
          name: "Public Administration",
          buildingId: "cpa-main-building",
          programs: [
            {
              id: "cpa-om",
              name: "Organization and Management",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Management Aptitude Test",
              ],
            },
            {
              id: "cpa-hrm",
              name: "Human Resource Management",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Management Aptitude Test",
              ],
            },
            {
              id: "cpa-lrga",
              name: "Local and Regional Government Administration",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Public Administration Aptitude Test",
              ],
            },
          ],
        },
        {
          id: "cpa-sw",
          name: "Social Work Department",
          buildingId: "cpa-main-building",
          programs: [
            {
              id: "cpa-bssw",
              name: "Bachelor of Science in Social Work (BSSW)",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Social Work Aptitude Test",
                "Interview",
              ],
            },
            {
              id: "cpa-msw",
              name: "Master in Social Work (MSW)",
              requirements: [
                "Bachelor's degree in Social Work or related field",
                "Social Work Experience",
                "Good Academic Standing",
              ],
            },
            {
              id: "cpa-mssw-prog",
              name: "Master of Science in Social Work (MSSW)",
              requirements: [
                "Bachelor's degree in Social Work or related field",
                "Social Work Experience",
                "Good Academic Standing",
              ],
            },
          ],
        },
        {
          id: "cpa-scd",
          name: "Sustainable Community Development",
          buildingId: "cpa-main-building",
          programs: [
            {
              id: "cpa-fcd",
              name: "Foundations of Community Development",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Community Development Interest",
              ],
            },
            {
              id: "cpa-isd",
              name: "Introduction to Sustainable Development",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Environmental Awareness",
              ],
            },
            {
              id: "cpa-ppsc",
              name: "Policies, Programs and Services in Community Development",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Social Science Background",
              ],
            },
          ],
        },
      ],
    },
    {
      id: "csper",
      name: "College of Sports Physical Education and Recreation",
      mainBuildingId: "csper-main-building",
      buildingIds: ["csper-main-building"],
      departments: [
        {
          id: "csper-professional",
          name: "Department of Professional Studies",
          buildingId: "csper-main-building",
          programs: [
            {
              id: "csper-bspe",
              name: "Bachelor of Science in Physical Education (BSPE)",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Physical Fitness Test",
                "Sports Skills Assessment",
              ],
            },
            {
              id: "csper-pdpe",
              name: "Professional Diploma in Physical Education (PDPE)",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Physical Fitness Test",
              ],
            },
            {
              id: "csper-mspe",
              name: "Master of Science in Physical Education (MSPE)",
              requirements: [
                "Bachelor's degree in Physical Education or related field",
                "Teaching/Coaching Experience",
                "Good Academic Standing",
              ],
            },
          ],
        },
        {
          id: "csper-pe",
          name: "Department of Physical Education",
          buildingId: "csper-main-building",
          programs: [
            {
              id: "csper-bspe-dept",
              name: "Bachelor of Science in Physical Education",
              requirements: [
                "High School Diploma",
                "Good Moral Certificate",
                "Medical Certificate",
                "Physical Fitness Test",
                "Sports Skills Assessment",
              ],
            },
          ],
        },
      ],
    },
  ],
  buildings: [
    {
      id: "coa007",
      name: "College of Agriculture Main Building",
      collegeId: "COA",
      type: "main",
      position: {x: -9, y: 1, z: 95},
      size: { w: 0, h: 0, d: 0 },
      color: "#079631",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "cbba003",
      name: "College of Business Administration and Accountancy Main Building",
      collegeId: "CBAA",
      type: "main",
      position: {x: -14, y: 1, z: 65},
      size: { w: 0, h: 0, d: 0 },
      color: "#ffdf00",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "coe",
      name: "College of Engineering Main Building",
      collegeId: "COE",
      type: "main",
      position: {x: 40, y: 1, z: 71},
      size: { w: 0, h: 0, d: 0 },
      color: "#ff0000",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "cnsm-new_building",
      name: "College of Natural Sciences and Mathematics Main Building",
      collegeId: "CNSM",
      type: "main",
      position: {x: 45, y: 1, z: -29},
      size: { w: 1, h: 1, d: 1 },
      color: "#ed2024",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "cssh-e",
      name: "College of Social Sciences and Humanities Main Building",
      collegeId: "CSSH",
      type: "main",
      position: {x: 42, y: 1, z: -0},
      size: { w: 0, h: 0, d: 0 },
      color: "#ffc637",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "ced",
      name: "College of Education Main Building",
      collegeId: "CED",
      type: "main",
      position: {x: 18, y: 1, z: 74},
      size: { w: 0, h: 0, d: 0 },
      color: "#067be5",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "division_of_engineering_technology",
      name: "Division of Engineering Technology Main Building",
      collegeId: "DET",
      type: "main",
      position: {x: -39, y: 2, z: -20},
      size: { w: 0, h: 0, d: 0 },
      color: "#FF271B",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "element633",
      name: "College of Fisheries and Aquatic Sciences Main Building",
      collegeId: "CFAS",
      type: "main",
      position: {x: 84, y: 1, z: -89},
      size: { w: 0, h: 0, d: 0 },
      color: "#1ac8d0",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "element1900",
      name: "College of Forestry and Environmental Studies Main Building",
      collegeId: "CFES",
      type: "main",
      position: {x: 87, y: 1, z: -76},
      size: { w: 0, h: 0, d: 0 },
      color: "#4c6839",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "element144",
      name: "College of Health Sciences Main Building",
      collegeId: "CHS",
      type: "main",
      position: {x: 212, y: 1, z: -76},
      size: { w: 0, h: 0, d: 0 },
      color: "#34762c",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "element034",
      name: "College of Hotel and Restaurant Management",
      collegeId: "CHARM",
      type: "main",
      position: {x: 40, y: 1, z: -102},
      size: { w: 0, h: 0, d: 0 },
      color: "#ff3470",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "college_of_information_and_computing_sciences",
      name: "College of Information and Computing Sciences Main Building",
      collegeId: "CICS",
      type: "main",
      position: {x: -15, y: 2, z: -73},
      size: { w: 0, h: 0, d: 0 },
      color: "#2e3192",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "element097",
      name: "King Faisal Center for Islamic Arabic and Asian Studies Main Building",
      collegeId: "KFCIAAS",
      type: "main",
      position: {x: 152, y: 1, z: -60},
      size: { w: 0, h: 0, d: 0 },
      color: "#2f936b",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "col",
      name: "College of Law Main Building",
      collegeId: "COL",
      type: "main",
      position: {x: 10, y: 1, z: 82},
      size: { w: 0, h: 0, d: 0 },
      color: "#6d1d52",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "cpa",
      name: "College of Public Affairs Main Building",
      collegeId: "CPA",
      type: "main",
      position: {x: 22, y: 1, z: 36},
      size: { w: 0, h: 0, d: 0 },
      color: "#006ee3",
      rooms: [], // To be filled based on 3D object names
    },
    {
      id: "cspear_and_grandstand",
      name: "College of Sports Physical Education and Recreation Main Building",
      collegeId: "CSPEAR",
      type: "main",
      position: {x: -33, y: 1, z: 18},
      size: { w: 0, h: 0, d: 0 },
      color: "#6c1a24",
      rooms: [], // To be filled based on 3D object names
    },
  ],
};

// Helper functions for the new data structure
export function findCollegeById(collegeId: string): College | null {
  return campus.colleges.find((c) => c.id === collegeId) || null;
}

export function findBuildingById(buildingId: string): Building | null {
  return campus.buildings.find((b) => b.id === buildingId) || null;
}

export function getDepartmentsByBuilding(buildingId: string): Department[] {
  for (const college of campus.colleges) {
    if (college.buildingIds.includes(buildingId)) {
      return college.departments.filter((d) => d.buildingId === buildingId);
    }
  }
  return [];
}

export function getProgramsByBuilding(buildingId: string): Program[] {
  const departments = getDepartmentsByBuilding(buildingId);
  return departments.flatMap((d) => d.programs);
}

export function getCollegeByBuilding(buildingId: string): College | null {
  return campus.colleges.find((c) => c.buildingIds.includes(buildingId)) || null;
}

export function findDepartmentById(
  departmentId: string
): { department: Department; college: College } | null {
  for (const college of campus.colleges) {
    const department = college.departments.find((d) => d.id === departmentId);
    if (department) {
      return { department, college };
    }
  }
  return null;
}

export function findProgramById(
  programId: string
): { program: Program; department: Department; college: College } | null {
  for (const college of campus.colleges) {
    for (const department of college.departments) {
      const program = department.programs.find((p) => p.id === programId);
      if (program) {
        return { program, department, college };
      }
    }
  }
  return null;
}

export function findRoomById(roomId: string) {
  const id = roomId.trim().toUpperCase();
  for (const building of campus.buildings) {
    const room = building.rooms.find((r) => r.id.toUpperCase() === id);
    if (room) {
      const world = {
        x: building.position.x + room.offset.x,
        y: building.position.y + room.offset.y + building.size.h / 2,
        z: building.position.z + room.offset.z,
      };
      return { room, building, world } as const;
    }
  }
  return null;
}

export function getBuildingsByCollege(collegeId: string): Building[] {
  return campus.buildings.filter((b) => b.collegeId === collegeId);
}

export function getMainBuildingByCollege(collegeId: string): Building | null {
  return (
    campus.buildings.find(
      (b) => b.collegeId === collegeId && b.type === "main"
    ) || null
  );
}
