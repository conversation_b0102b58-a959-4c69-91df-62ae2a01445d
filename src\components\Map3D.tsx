"use client";
import { <PERSON><PERSON>, use<PERSON><PERSON><PERSON>, useThree } from "@react-three/fiber";
import { OrbitControls, Html, useGLTF, useProgress } from "@react-three/drei";
import * as THREE from "three";
import { campus, Building } from "@/data/campus";
import React, { useEffect, useRef, useCallback, Suspense, useState } from "react";
import type { OrbitControls as OrbitControlsImpl } from "three-stdlib";
import { on } from "events";

export type Map3DProps = {
  onSelectBuilding?: (b: Building | null) => void;
  focusTarget?: THREE.Vector3 | null;
  // New: highlight selected building
  selectedBuildingId?: string | null;
  // New: optional label for focus target (e.g., room id)
  focusLabel?: string | null;
  selected?: Building | null;
};

function Ground() {
  return (
    <mesh rotation-x={-Math.PI / 2} receiveShadow>
      <planeGeometry args={[1000, 1000]} />
      <meshStandardMaterial color="#a7f3d0" />
    </mesh>
  );
}

// Simple GLB model component
function Model({ url }: { url: string }) {
  const gltf = useGLTF(url);

  useEffect(() => {
    gltf.scene.traverse((obj) => {
      if (obj instanceof THREE.Mesh) {
        obj.castShadow = true;
        obj.receiveShadow = true;
        if (!obj.name) obj.name = obj.parent?.name || "Mesh";
      }
    });
  }, [gltf.scene]);

  return <primitive object={gltf.scene} />;
}

// Match a clicked object's name (or any ancestor's) to a campus building
function findBuildingFromObject(obj: THREE.Object3D | null): Building | null {
  const norm = (s: string) => s.trim().toLowerCase();
  const tryMatch = (name: string): Building | null => {
    const n = norm(name);
    if (!n) return null;
    // exact name match
    let b = campus.buildings.find((bb) => norm(bb.id) === n);
    if (b) return b;
    // object name contains building name
    b = campus.buildings.find((bb) => n.includes(norm(bb.name)) || norm(bb.name).includes(n));
    return b || null;
  };
  let cur: THREE.Object3D | null = obj;
  while (cur) {
    if (cur.name) {
      const b = tryMatch(cur.name);
      if (b) return b;
    }
    cur = cur.parent;
  }
  return null;
}

// Toggle highlight on a mesh or group of meshes by adjusting emissive
function setHighlight(root: THREE.Object3D, enabled: boolean) {
  root.traverse((o) => {
    if (!(o instanceof THREE.Mesh)) return;
    const apply = (m: THREE.Material) => {
      const mat = m as unknown as { emissive?: THREE.Color; emissiveIntensity?: number; userData: Record<string, unknown> };
      if (mat.emissive) {
        if (enabled) {
          if (!mat.userData.__origEmissive) {
            mat.userData.__origEmissive = mat.emissive.clone();
            mat.userData.__origEmissiveIntensity = mat.emissiveIntensity ?? undefined;
          }
          mat.emissive.set("#fde047");
          if (typeof mat.emissiveIntensity === "number") mat.emissiveIntensity = 0.8;
        } else {
          const orig = mat.userData.__origEmissive as THREE.Color | undefined;
          if (orig) mat.emissive.copy(orig);
          const origI = mat.userData.__origEmissiveIntensity as number | undefined;
          if (typeof origI === "number" && typeof mat.emissiveIntensity === "number") mat.emissiveIntensity = origI;
        }
      } else {
        // Fallback: slightly tint basic materials by vertex colors via color if available
        const mb = m as unknown as { color?: THREE.Color; userData: Record<string, unknown> };
        if (mb.color) {
          if (enabled) {
            if (!mb.userData.__origColor) mb.userData.__origColor = mb.color.clone();
            mb.color.offsetHSL(0, 0, 0.2);
          } else {
            const oc = mb.userData.__origColor as THREE.Color | undefined;
            if (oc) mb.color.copy(oc);
          }
        }
      }
    };
    const mm = o.material as THREE.Material | THREE.Material[] | undefined;
    if (!mm) return;
    if (Array.isArray(mm)) mm.forEach(apply);
    else apply(mm);
  });
}

function LoadingOverlay() {
  const { progress } = useProgress();
  return (
    <Html center>
      <div className="px-3 py-2 text-sm rounded bg-black/70 text-white shadow">
        Loading 3D model… {Math.round(progress)}%
      </div>
    </Html>
  );
}

function FocusMarker({ target, label }: { target: THREE.Vector3; label?: string | null }) {
  const group = useRef<THREE.Group>(null);
  useFrame((state) => {
    if (group.current) {
      const t = state.clock.getElapsedTime();
      const s = 1 + Math.sin(t * 3) * 0.15;
      group.current.scale.setScalar(s);
    }
  });
  return (
    <group ref={group} position={[target.x, target.y + 0.2, target.z]}>
      <mesh>
        <sphereGeometry args={[0.6, 24, 24]} />
        <meshStandardMaterial color="#f59e0b" emissive="#f59e0b" emissiveIntensity={0.8} />
      </mesh>
      <mesh position={[0, -0.25, 0]} rotation={[-Math.PI / 2, 0, 0]}>
        <ringGeometry args={[0.6, 0.9, 32]} />
        <meshBasicMaterial color="#f59e0b" transparent opacity={0.9} />
      </mesh>
      {label ? (
        <Html position={[0, 1.2, 0]} center style={{ pointerEvents: "none" }}>
          <div className="px-2 py-1 text-xs rounded bg-black/70 text-white shadow">{label}</div>
        </Html>
      ) : null}
    </group>
  );
}

function CameraBoundaryControls({ focusTarget }: { focusTarget?: THREE.Vector3 | null }) {
  const controls = useRef<OrbitControlsImpl | null>(null);

  useEffect(() => {
    if (!controls.current) return;
    // Touch: one-finger pan, two-finger dolly/pan
    // TOUCH.PAN = 1, TOUCH.DOLLY_PAN = 2
    controls.current.touches.ONE = 1;
    controls.current.touches.TWO = 2;

    // Mouse: left/right drag pan, middle dolly
    controls.current.mouseButtons.LEFT = THREE.MOUSE.PAN;
    controls.current.mouseButtons.MIDDLE = THREE.MOUSE.DOLLY;
    controls.current.mouseButtons.RIGHT = THREE.MOUSE.PAN;

    // Lock polar angle to 45° and disable rotate for a map-like experience
    controls.current.minPolarAngle = Math.PI / 4;
    controls.current.maxPolarAngle = Math.PI / 4;
    controls.current.enableRotate = false;
    controls.current.update();
  }, []);
  useFrame(({ camera }) => {
    // Smooth focus when provided
    if (focusTarget) {
      if (controls.current) {
        // Smoothly move target to focus position
        controls.current.target.lerp(new THREE.Vector3(focusTarget.x, 0, focusTarget.z), 0.12);
        // Lock camera rotation by maintaining consistent relative position to target
        const idealCameraPosition = new THREE.Vector3(
          controls.current.target.x,
          60,
          controls.current.target.z + 60
        );
        camera.position.lerp(idealCameraPosition, 0.12);

        // Ensure camera looks at the target
        camera.lookAt(controls.current.target);
        controls.current.update();
      }
    }

    // Constrain panning within campus bounds without changing view angle
    if (controls.current) {
      const t = controls.current.target;
      const beforeX = t.x;
      const beforeZ = t.z;

      // Clamp target to bounds
      t.x = Math.min(Math.max(t.x, campus.boundary.minX), campus.boundary.maxX);
      t.z = Math.min(Math.max(t.z, campus.boundary.minZ), campus.boundary.maxZ);
      t.y = 0; // keep target on ground

      // Translate camera by the same delta to prevent any unintended rotation/tilt
      const dx = t.x - beforeX;
      const dz = t.z - beforeZ;
      if (dx !== 0 || dz !== 0) {
        camera.position.x += dx;
        camera.position.z += dz;
        controls.current.update();
      }
    }
  });

  return (
    <OrbitControls
      ref={controls}
      enableRotate={false}
      enablePan
      enableZoom
      makeDefault
      screenSpacePanning={false}
      // For orthographic camera, control zoom via min/maxZoom
      minZoom={4}
      maxZoom={20}
    />
  );
}

function RaycastPicker({ getTargets, onPickBuilding, onIntersect }: { getTargets: () => THREE.Object3D[]; onPickBuilding?: (b: Building | null) => void; onIntersect?: (hit: THREE.Intersection | null) => void }) {
  const { camera, gl } = useThree();
  const raycasterRef = useRef(new THREE.Raycaster());
  const pointer = useRef(new THREE.Vector2());
  const gesture = useRef({ downX: 0, downY: 0, dragging: false });

  useEffect(() => {
    const onPointerDown = (e: PointerEvent) => {
      gesture.current.downX = e.clientX;
      gesture.current.downY = e.clientY;
      gesture.current.dragging = false;
    };
    const onPointerMove = (e: PointerEvent) => {
      if (gesture.current.dragging) return;
      const dx = Math.abs(e.clientX - gesture.current.downX);
      const dy = Math.abs(e.clientY - gesture.current.downY);
      if (dx + dy > 6) {
        gesture.current.dragging = true; // treat as pan, don't pick
      }
    };
    const onPointerUp = (e: PointerEvent) => {
      if (gesture.current.dragging) return; // ignore pans/drags

      const rect = gl.domElement.getBoundingClientRect();
      pointer.current.x = ((e.clientX - rect.left) / rect.width) * 2 - 1;
      pointer.current.y = -((e.clientY - rect.top) / rect.height) * 2 + 1;

      raycasterRef.current.setFromCamera(pointer.current, camera);
      const targets = getTargets();
      const intersects = targets.length ? raycasterRef.current.intersectObjects(targets, true) : [];

      const hit = intersects[0] ?? null;
      onIntersect?.(hit ?? null);

      if (!hit) {
        onPickBuilding?.(null);
        return;
      }

      // If a building was clicked (when buildings are enabled), surface it
      let obj: THREE.Object3D | null = hit.object;
      let id: string | null = null;
      while (obj) {
        const ud = (obj as THREE.Object3D).userData as Record<string, unknown>;
        const maybe = typeof ud.name === "string" ? (ud.name as string) : undefined;
        if (maybe) { id = maybe; break; }
        obj = obj.parent;
      }

      if (id) {
        const b = campus.buildings.find((bb) => bb.id === id) || null;
        onPickBuilding?.(b);
      } else {
        onPickBuilding?.(null);
      }
    };

    const el = gl.domElement;
    el.addEventListener("pointerdown", onPointerDown);
    el.addEventListener("pointermove", onPointerMove);
    el.addEventListener("pointerup", onPointerUp);
    return () => {
      el.removeEventListener("pointerdown", onPointerDown);
      el.removeEventListener("pointermove", onPointerMove);
      el.removeEventListener("pointerup", onPointerUp);
    };
  }, [camera, gl, getTargets, onPickBuilding, onIntersect]);

  return null;
}

function WebGLContextGuard() {
  const { gl } = useThree();
  useEffect(() => {
    const canvas = gl.domElement;
    const onLost = (e: Event) => {
      e.preventDefault();
      console.warn("WebGL context lost. Attempting to restore...");
    };
    const onRestored = () => {
      console.info("WebGL context restored.");
    };
    canvas.addEventListener("webglcontextlost", onLost as EventListener, false);
    canvas.addEventListener("webglcontextrestored", onRestored as EventListener, false);
    return () => {
      canvas.removeEventListener("webglcontextlost", onLost as EventListener);
      canvas.removeEventListener("webglcontextrestored", onRestored as EventListener);
    };
  }, [gl]);
  return null;
}

export default function Map3D({ focusTarget, focusLabel, onSelectBuilding, selected }: Map3DProps) {
  // Reference to the GLTF model root for picking
  const modelRef = useRef<THREE.Object3D | null>(null);
  const [selectedObj, setSelectedObj] = useState<THREE.Object3D | null>(null);

  // For this test, only pick the model (disable campus buildings)
  const getTargets = useCallback(() => (modelRef.current ? [modelRef.current] : []), []);

  useEffect(() => {
    if (!selected) {
      if (selectedObj) setHighlight(selectedObj, false);
      setSelectedObj(null);
      onSelectBuilding?.(null);
      return;
    }
  }, [selected, onSelectBuilding, selectedObj]);

  return (
    <div className="w-full h-[70svh] md:h-[78svh] bg-sky-50 rounded-lg overflow-hidden border relative overscroll-contain">      <Canvas
      shadows
      orthographic
      dpr={[1, 1]}
      gl={{ antialias: true, powerPreference: "high-performance", alpha: false }}
      camera={{ position: [0, 60, 60], zoom: 5, near: 0.1, far: 2000 }}
      style={{ width: "100%", height: "100%", display: "block" }}
    >
      <WebGLContextGuard />
      {/* Match scene background with container to hide any exposed area beyond the ground */}
      <color attach="background" args={["#f0f9ff"]} />
      <ambientLight intensity={0.6} />
      <directionalLight position={[50, 80, 40]} intensity={0.8} castShadow shadow-mapSize={[1024, 1024]} />
      <group>
        <Ground />
        {/* Disabled campus boxes for now to focus on GLB */}
        {/* <group>{campus.buildings.map((b) => (<BuildingBox key={b.id} b={b} onMeshReady={() => {}} isSelected={false} />))}</group> */}
        {/* Load your GLB from public. Move file to /public and reference as "/scene.glb" */}
        <Suspense fallback={<LoadingOverlay />}>
          <group ref={modelRef}>
            <Model url="/scene.glb" />
          </group>          </Suspense>
        {focusTarget ? <FocusMarker target={focusTarget} label={focusLabel} /> : null}
        <BuildingLabels />
      </group>
      <CameraBoundaryControls focusTarget={focusTarget ?? null} />
      <RaycastPicker
        getTargets={getTargets}
        onIntersect={(hit) => {
          // Clear selection on empty space
          if (!hit) {
            if (selectedObj) setHighlight(selectedObj, false);
            setSelectedObj(null);
            onSelectBuilding?.(null);
            return;
          }
          console.log(`position: {x: ${hit.point.x.toFixed(0)}, y: ${hit.point.y.toFixed(0)}, z: ${hit.point.z.toFixed(0)}},`);
          // Select clicked mesh and try to match a campus building by name
          const obj = hit.object as THREE.Object3D;
          const defaultMat = ['wall', 'rooftop'];
          console.log(obj.name)
          const isElemMat = obj instanceof THREE.Mesh && defaultMat.includes(obj.material.name)
          if (selectedObj && selectedObj !== obj) setHighlight(selectedObj, false);
          if (!isElemMat) setHighlight(obj, true);
          setSelectedObj(obj);
          const building = findBuildingFromObject(obj);
          if (building) {
            onSelectBuilding?.(building);

          } else {
            onSelectBuilding?.(null);
            // keep highlight even if no campus match so user sees the selection
          }
        }}
      />
      <BuildingLabels />
    </Canvas>
    </div>
  );
}

// Optionally preload the model
useGLTF.preload?.("/scene.glb");


function BuildingLabels() {
  const { camera } = useThree();
  const [cameraDistance, setCameraDistance] = useState(0);

  useFrame(() => {
    // Calculate camera distance from target (approximate zoom level)
    // For orthographic camera, we can use camera.zoom or camera.position.y
    if (camera instanceof THREE.OrthographicCamera) {
      setCameraDistance(camera.zoom);
    } else {
      // For perspective camera, use distance from origin
      setCameraDistance(camera.position.distanceTo(new THREE.Vector3(0, 0, 0)));
    }
  });

  // Show labels when camera is zoomed in enough
  // Adjust these values based on your camera setup
  const minZoomForLabels = 4; // Show labels when zoom > 8
  const showLabels = cameraDistance > minZoomForLabels;

  if (!showLabels) return null;

  return (
    <>
      {campus.buildings.map((building) => (
        <group key={building.id} position={[building.position.x, building.position.y + 8, building.position.z]}>
          <Html
            center
            style={{
              pointerEvents: "none",
              userSelect: "none",
            }}
          >
            <div
              className="px-3 py-2 text-sm font-medium rounded-lg shadow-lg transition-all duration-300"
              style={{
                backgroundColor: building.color || "#ffffff",
                color: getContrastColor(building.color || "#ffffff"),
                border: "2px solid rgba(255, 255, 255, 0.3)",
                backdropFilter: "blur(4px)",
                maxWidth: "200px",
                textAlign: "center",
                fontSize: "12px",
                lineHeight: "1.2",
                opacity: 0.9,
              }}
            >
              {building.collegeId}
            </div>
          </Html>
        </group>
      ))}
    </>
  );
}

// Helper function to determine contrasting text color
function getContrastColor(hexColor: string): string {
  // Remove # if present
  const hex = hexColor.replace('#', '');

  // Convert to RGB
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

  // Return black for light colors, white for dark colors
  return luminance > 0.5 ? '#000000' : '#ffffff';
}
