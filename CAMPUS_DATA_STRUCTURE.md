# Campus Data Structure - MSU Tara

## Overview

The campus data structure has been completely restructured to properly represent the real-world organization of Mindanao State University (MSU). The new structure follows a hierarchical organization that mirrors how universities actually work.

## Data Structure Hierarchy

```
Campus
├── Colleges (17 colleges)
│   ├── Departments (Multiple per college)
│   │   └── Programs (Multiple per department)
│   │       └── Requirements (Program-specific)
│   └── Buildings (One main + optional additional buildings)
│       └── Rooms (Multiple per building)
```

## Key Types

### College
- **id**: Short identifier matching 3D mesh materials (e.g., "coe", "cnsm")
- **name**: Full college name
- **mainBuildingId**: Reference to the main building
- **buildingIds**: Array of all buildings belonging to this college
- **departments**: Array of departments in this college

### Department
- **id**: Unique department identifier
- **name**: Department name
- **buildingId**: Which building this department is located in
- **programs**: Array of programs offered by this department

### Program
- **id**: Unique program identifier
- **name**: Program name
- **requirements**: Array of enrollment/admission requirements

### Building
- **id**: Matches 3D mesh material name (e.g., "coe-main-building")
- **name**: Building name
- **collegeId**: Which college this building belongs to
- **type**: "main" or "additional"
- **position**: 3D coordinates (to be filled manually)
- **size**: Building dimensions (to be filled manually)
- **color**: Hex color for 3D visualization
- **rooms**: Array of rooms in this building

### Room
- **id**: Unique room identifier based on 3D object name
- **name**: Optional room name
- **offset**: Position relative to building center

## Complete List of Colleges

1. **COA** - College of Agriculture
2. **CBAA** - College of Business Administration and Accountancy
3. **CED** - College of Education
4. **COE** - College of Engineering
5. **DET** - Division of Engineering Technology
6. **CFAS** - College of Fisheries and Aquatic Sciences
7. **CFES** - College of Forestry and Environmental Studies
8. **CHS** - College of Health Sciences
9. **CHTM** - College of Hospitality and Tourism Management
10. **CICS** - College of Information and Computing Sciences
11. **ISE** - Institute of Science Education
12. **KFCIAAS** - King Faisal Center for Islamic Arabic and Asian Studies
13. **COL** - College of Law
14. **CNSM** - College of Natural Sciences and Mathematics
15. **CPA** - College of Public Affairs
16. **CSSH** - College of Social Sciences and Humanities
17. **CSPER** - College of Sports Physical Education and Recreation

## Building ID Convention

Buildings follow the pattern: `{college-id}-{building-type}-building`
- Main buildings: `{college-id}-main-building`
- Additional buildings: `{college-id}-building-1`, `{college-id}-building-2`, etc.

## Helper Functions Available

```typescript
// Find entities by ID
findCollegeById(collegeId: string): College | null
findBuildingById(buildingId: string): Building | null
findDepartmentById(departmentId: string): { department: Department; college: College } | null
findProgramById(programId: string): { program: Program; department: Department; college: College } | null
findRoomById(roomId: string): { room: Room; building: Building; world: Position } | null

// Get related entities
getBuildingsByCollege(collegeId: string): Building[]
getMainBuildingByCollege(collegeId: string): Building | null
```

## Next Steps

### For 3D Integration:
1. **Position Buildings**: Fill in the `position` and `size` fields for each building based on your 3D model coordinates
2. **Add Rooms**: Create room objects based on the 3D object names in your models
3. **Material Mapping**: Ensure 3D mesh materials match the building IDs (e.g., "coe-main-building")

### For Data Management:
1. **Requirements Update**: Update the sample requirements with actual MSU requirements
2. **Room Details**: Add specific room information based on actual campus layout
3. **Additional Buildings**: Add more buildings for colleges that have multiple buildings

## Example Usage

```typescript
// Find a college
const coe = findCollegeById("coe");

// Get all buildings for College of Engineering
const coeBuildings = getBuildingsByCollege("coe");

// Find a specific program
const civilEng = findProgramById("coe-bsce");

// Find a room and get its world coordinates
const room = findRoomById("EMB-201");
if (room) {
  console.log(`Room ${room.room.id} is at coordinates:`, room.world);
}
```

## Notes

- All positions and sizes are currently set to (0,0,0) and need to be filled manually based on your 3D model
- Room arrays are empty and should be populated based on 3D object names
- Each college has exactly one main building; additional buildings can be added as needed
- Requirements are sample data and should be updated with actual MSU requirements
