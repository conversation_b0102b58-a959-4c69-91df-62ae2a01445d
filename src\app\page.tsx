"use client";
import React, { useEffect, useMemo, useState } from "react";
import dynamic from "next/dynamic";
import * as THREE from "three";
import { campus, Building } from "@/data/campus";
import BuildingPanel from "@/components/BuildingPanel";
import RoomSearch from "@/components/RoomSearch";
import Chatbot from "@/components/Chatbot";

const Map3D = dynamic(() => import("@/components/Map3D"), { ssr: false });

type Vec3 = { x: number; y: number; z: number };

export default function Page() {
  const [selected, setSelected] = useState<Building | null>(null);
  const [focus, setFocus] = useState<Vec3 | null>(null);
  const [focusLabel, setFocusLabel] = useState<string | null>(null);

  useEffect(() => {
    if (selected) {
      setFocus({
        x: selected.position.x,
        y: selected.position.y,
        z: selected.position.z,
      });
    } else {
      setFocus(null);
      setFocusLabel(null);
    }
  }, [selected]);

  const focusVector: THREE.Vector3 | null = useMemo(() => {
    if (!focus) return null;
    return new THREE.Vector3(focus.x, focus.y, focus.z);
  }, [focus]);

  return (
    <div className="max-w-7xl mx-auto px-4 py-6 space-y-6">
      <section className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-semibold tracking-tight">
            Explore MSU - Main Campus
          </h2>
          <p className="text-slate-600 text-sm">
            Interactive 3D campus map, building info, room finder, and academic
            Q&amp;A.
          </p>
        </div>
        <RoomSearch
          onFocus={(w) => setFocus(w)}
          onSelectBuildingId={(id) => {
            const b = campus.buildings.find((bb) => bb.id === id) || null;
            setSelected(b);
          }}
          onFocusLabel={(label) => setFocusLabel(label)}
        />
      </section>

      <Map3D
        onSelectBuilding={(b) => setSelected(b)}
        focusTarget={focusVector}
        selectedBuildingId={selected?.id ?? null}
        focusLabel={focusLabel}
        selected={selected}
      />

      <BuildingPanel building={selected} onClose={() => setSelected(null)} />
      <Chatbot />
    </div>
  );
}
