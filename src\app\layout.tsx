import "./globals.css";
import React from "react";

export const metadata = {
  title: "MSU - Main Campus Guide",
  description: "Interactive campus guide with map, room finder, and chatbot.",
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className="antialiased bg-slate-50 text-slate-900">
        <div id="portal-root" />
        <div className="min-h-dvh flex flex-col">
          <header className="sticky top-0 z-20 backdrop-blur bg-white/70 border-b border-slate-200">
            <div className="max-w-7xl mx-auto px-4 py-3 flex items-center justify-between">
              <h1 className="font-semibold tracking-tight">MSU - Main Campus Guide</h1>
              <a className="text-sm text-blue-600 hover:underline" href="https://www.msumain.edu.ph/" target="_blank" rel="noreferrer">Official Site</a>
            </div>
          </header>
          <main className="flex-1">{children}</main>
          <footer className="border-t border-slate-200 py-4 text-center text-sm text-slate-500">
            Sample map for demonstration only.
          </footer>
        </div>
      </body>
    </html>
  );
}
