"use client";
import { findRoomById } from "@/data/campus";
import { useState } from "react";

export type RoomSearchProps = {
  onFocus: (world: { x: number; y: number; z: number } | null) => void;
  onSelectBuildingId: (id: string | null) => void;
  // New: optional label setter for focus marker
  onFocusLabel?: (label: string | null) => void;
};

export default function RoomSearch({ onFocus, onSelectBuildingId, onFocusLabel }: RoomSearchProps) {
  const [query, setQuery] = useState("");
  const [error, setError] = useState<string | null>(null);

  function submit() {
    const res = findRoomById(query);
    if (!res) {
      setError("Room not found.");
      onFocus(null);
      onSelectBuildingId(null);
      onFocusLabel?.(null);
      return;
    }
    setError(null);
    onFocus(res.world);
    onSelectBuildingId(res.building.id);
    onFocusLabel?.(`${res.room.id}${res.room.name ? ` - ${res.room.name}` : ""}`);
  }

  return (
    <div className="flex gap-2 w-full max-w-xl">
      <input
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onKeyDown={(e) => e.key === 'Enter' && submit()}
        placeholder="Find a room e.g. EMB-07 or RM304"
        className="flex-1 border rounded px-3 py-2 text-sm outline-none focus:ring-2 focus:ring-blue-200"
      />
      <button onClick={submit} className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm">Search</button>
      {error && <div className="text-sm text-red-600 self-center">{error}</div>}
    </div>
  );
}
