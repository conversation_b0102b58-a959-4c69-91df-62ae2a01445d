"use client";
import React, { useEffect, useRef, useState } from "react";
import { PaperAirplaneIcon, ChatBubbleLeftRightIcon } from "@heroicons/react/24/solid";

export default function Chatbot() {
  const [open, setOpen] = useState(false);
  const [messages, setMessages] = useState<{ role: "user" | "assistant"; content: string }[]>(
    [
      { role: "assistant", content: "Hi! I'm your MSU academic assistant. Ask me about programs, requirements, or departments." },
    ],
  );
  const [input, setInput] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (open) inputRef.current?.focus();
  }, [open]);

  async function send() {
    const q = input.trim();
    if (!q) return;
    setInput("");
    setMessages((m) => [...m, { role: "user", content: q }]);
    try {
      const res = await fetch("/api/chat", { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify({ question: q }) });
      const data = await res.json();
      setMessages((m) => [...m, { role: "assistant", content: data.answer || "Sorry, I couldn't find that." }]);
    } catch {
      setMessages((m) => [...m, { role: "assistant", content: "Network error." }]);
    }
  }

  return (
    <>
      <button
        className="fixed bottom-4 right-4 z-40 rounded-full bg-blue-600 text-white p-4 shadow-lg hover:bg-blue-700 focus:outline-none"
        onClick={() => setOpen((o) => !o)}
        aria-label="Toggle chatbot"
      >
        <ChatBubbleLeftRightIcon className="h-6 w-6" />
      </button>
      <div className={`fixed bottom-20 right-4 z-40 w-[22rem] max-w-[calc(100vw-2rem)] bg-white border shadow-xl rounded-lg overflow-hidden transition-transform duration-300 ${open ? 'translate-y-0' : 'translate-y-[140%]'}`}>
        <div className="p-3 border-b font-medium">MSU Academic Q&A</div>
        <div className="h-64 overflow-y-auto p-3 space-y-3 text-sm">
          {messages.map((m, i) => (
            <div key={i} className={`flex ${m.role === 'user' ? 'justify-end' : 'justify-start'}`}>
              <div className={`max-w-[80%] px-3 py-2 rounded ${m.role === 'user' ? 'bg-blue-600 text-white' : 'bg-slate-100 text-slate-900'}`}>{m.content}</div>
            </div>
          ))}
        </div>
        <div className="p-2 border-t flex items-center gap-2">
          <input
            ref={inputRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={(e) => e.key === 'Enter' && send()}
            placeholder="Ask about programs, requirements..."
            className="flex-1 border rounded px-2 py-2 text-sm outline-none focus:ring-2 focus:ring-blue-200"
          />
          <button onClick={send} className="p-2 rounded bg-blue-600 text-white hover:bg-blue-700">
            <PaperAirplaneIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </>
  );
}
